using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.IhcRepayment;
using BlueTape.PaymentService.CompatibilityService.Extensions;
using BlueTape.PaymentService.CompatibilityService.Models;
using BlueTape.PaymentService.CompatibilityService.Services.Base;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Transaction;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums.Legacy;
using BlueTape.PaymentService.Domain.Extensions;
using MongoDB.Bson;
using System.Globalization;

namespace BlueTape.PaymentService.CompatibilityService.Services.IhcRepayment;

public class IhcRepaymentCompatibilityMapper(IBankAccountRepository bankAccountRepository) : BaseCompatibilityMapper(bankAccountRepository), IIhcRepaymentCompatibilityMapper
{
    public override OperationEntity MapFromPaymentRequestToOperation(PaymentRequestEntity paymentRequest, string ownerId)
    {
        var operationStatus = paymentRequest.GetDrawRepaymentOperationStatus().ToString();
        var firstTransaction = paymentRequest.Transactions.Where(x => x.TransactionType == PaymentTransactionType.AchPull).MinBy(x => x.CreatedAt);

        return new OperationEntity()
        {
            BlueTapeId = ObjectId.GenerateNewId().ToString()!,
            OwnerId = ownerId,
            Status = operationStatus,
            Type = paymentRequest.RequestType.MapFromPaymentRequestTypeToOperationType(),
            Amount = paymentRequest.Amount,
            Date = paymentRequest.Date.ToDateTime(new TimeOnly(), DateTimeKind.Utc),
            Metadata = new OperationMetadataEntity
            {
                PayerId = paymentRequest.PayerId ?? string.Empty,
                PaymentMethod = paymentRequest.PaymentMethod.ToString().ToLower(),
                PaymentDate = paymentRequest.Date.ToString(),
                LmsPaymentId = paymentRequest.PaymentRequestDetails?.LMSPaymentId.ToString()
            },
            PaymentRequestId = paymentRequest.Id.ToString(),
            CreatedBy = DomainConstants.PaymentService,
            FirstTransactionDate = firstTransaction?.CreatedAt,
            PaymentProvider = PaymentProvider.Aion.ToString()
        };
    }

    public async Task<List<TransactionEntity>> MapFromPaymentTransactionsToLegacyTransactions(PaymentRequestEntity paymentRequest, OperationEntity operation, CancellationToken ctx)
    {
        var transaction = paymentRequest.Transactions.MaxBy(e => e.CreatedAt) ?? paymentRequest.Transactions.Last();
        var operationId = operation.BlueTapeId;
        var payerId = paymentRequest.PayerId;

        var metadata = await GetInhRepaymentTransactionMetadata(transaction, transaction.Amount, LegacyTransactionType.PULL, ctx);

        var mappedTransaction = new TransactionEntity()
        {
            OperationId = operationId ?? string.Empty,
            Type = PaymentTransactionType.AchPull.ToString().ToLower(),
            PayerId = payerId,
            Amount = transaction.Amount,
            Currency = transaction.Currency,
            Fee = 0,
            PaymentMethod = PaymentMethod.Ach.ToString().ToLower(),
            Status = OperationStatus.PLACED.ToString(),
            CreatedBy = paymentRequest.CreatedBy,
            Provider = PaymentProvider.Aion.ToString(),
            PaymentTransactionId = transaction.Id.ToString(),
            PaymentRequestId = paymentRequest.Id.ToString(),
            Date = transaction.Date.ToDateTime(new TimeOnly()),
            Metadata = metadata,
        };

        return [mappedTransaction];
    }

    public override async Task<UpdateTransactionEntity> MapSyncModelToUpdateTransactionEntity(SyncTransactionModel syncTransactionModel, CancellationToken ctx)
    {
        var accountNumber = await GetAccountNumber(syncTransactionModel.ReceiverAccountId ?? string.Empty, ctx);

        var updateTransactionEntity = new UpdateTransactionEntity
        {
            Date = syncTransactionModel.Date,
            Status = syncTransactionModel.Status.MapFromPaymentTransactionStatusToLegacyTransactionStatus().ToString(),
            MetadataAccountNumber = accountNumber,
            StatusDataAccountId = syncTransactionModel.ReceiverAccountId,
            StatusDataTransactionNumber = syncTransactionModel.ReferenceNumber,
            OriginalReference = syncTransactionModel.TransactionNumber,
            MetadataTransactionNumber = syncTransactionModel.ReferenceNumber,
            ApiDateTime = syncTransactionModel.ExecutedAt,
            UpdatedBy = DomainConstants.PaymentService,
            StatusCode = syncTransactionModel.StatusCode,
            PaymentTransactionId = syncTransactionModel.PaymentTransactionId,
            Provider = syncTransactionModel.Provider,
            PublicTransactionNumber = syncTransactionModel.PublicTransactionNumber,
            StatusReason = syncTransactionModel.StatusReason,
            StatusDataTransactionStatus = syncTransactionModel.StatusDataTransactionStatus
        };

        return updateTransactionEntity;
    }

    private async Task<TransactionMetadataEntity> GetInhRepaymentTransactionMetadata(PaymentTransactionEntity? transaction, decimal legacyTransactionAmount, LegacyTransactionType? transactionType, CancellationToken ctx)
    {
        var accountNumber = string.Empty;

        if (!string.IsNullOrEmpty(transaction?.ReceiverAccountId))
        {
            var bankAccount = (await bankAccountRepository.GetById(transaction?.ReceiverAccountId!, ctx));
            accountNumber = bankAccount.AccountNumber?.Display ?? string.Empty;
        }
        if (transaction?.Status == TransactionStatus.Placed)
        {
            return new TransactionMetadataEntity()
            {
                TransactionType = transactionType.ToString(),
                AccountNumber = accountNumber,
                PublicTransactionNumber = transaction.PublicTransactionNumber,
                LmsPaymentId = transaction?.PaymentRequest?.PaymentRequestDetails?.LMSPaymentId.ToString(),
                StatusData = new TransactionStatusDataEntity()
                {
                    TransactionAmountCents = (double)legacyTransactionAmount * 100,
                    TransactionFeeCents = 0
                }
            };
        }

        return new TransactionMetadataEntity()
        {
            TransactionType = transactionType.ToString(),
            AccountNumber = accountNumber,
            AccountId = transaction?.ReceiverAccountId,
            PublicTransactionNumber = transaction?.PublicTransactionNumber,
            LmsPaymentId = transaction?.PaymentRequest?.PaymentRequestDetails?.LMSPaymentId.ToString(),
            StatusData = new TransactionStatusDataEntity()
            {
                TransactionAmountCents = (double)legacyTransactionAmount * 100,
                TransactionFeeCents = 0, // in review
                Account = new TransactionAccountEntity()
                {
                    AccountId = transaction?.OriginatorAccountId
                },
                TransactionNumber = transaction?.ReferenceNumber,
                ApiMetadata = new TransactionApiMetadataEntity()
                {
                    DateTime = transaction?.ExecutedAt.ToString(CultureInfo.InvariantCulture),
                    OriginalReference = transaction?.TransactionNumber,
                    Reference = transaction?.TransactionNumber,
                    Type = transactionType == LegacyTransactionType.PULL ?
                        LegacyPaymentFlowConstants.TransactionApiMetadataPullType
                        : LegacyPaymentFlowConstants.TransactionApiMetadataOutType,
                },
                StatusCode = LegacyPaymentFlowConstants.TransactionMetadataStatusCode
            }
        };
    }
}
