﻿

using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.Document.DataAccess.EF.Abstractions;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories.Base;
using BlueTape.PaymentService.DataAccess.Constants;
using BlueTape.PaymentService.DataAccess.Contexts;
using BlueTape.PaymentService.DataAccess.Repositories;
using BlueTape.PaymentService.DataAccess.Repositories.Base;
using BlueTape.PaymentService.DataAccess.Triggers.Aborted;
using BlueTape.PaymentService.DataAccess.Triggers.Forbid;
using BlueTape.Utilities.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.PaymentService.DataAccess.DI;

public static class DependencyRegistrar
{
    public static void AddDataAccessDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);

        services.AddDbContext<DatabaseContext>(context =>
        {
            if (string.IsNullOrEmpty(env) || env.Equals(EnvironmentConstants.Local) || env.Equals(EnvironmentConstants.Development) || env.Equals(EnvironmentConstants.IntegrationTest))
            {
                var conn = configuration.GetConnectionString(DiConstants.LocalDbConnection);
                context.UseNpgsql(conn);
            }
            else
            {
                var connectionString = configuration.GetSection(DiConstants.AzureDbConnection).Value;

                if (string.IsNullOrEmpty(connectionString))
                {
                    var provider = services.BuildServiceProvider();
                    var keyVaultService = provider.GetRequiredService<IKeyVaultService>();
                    connectionString = keyVaultService.GetSecret(DiConstants.AzureDbConnection).GetAwaiter().GetResult();
                }
                //connectionString = "Host=**********;Port=5432;Database=payment-beta;Username=paymentbeta;Password=***************;";
                connectionString = "Host=**********;Port=5432;Database=payment-qa;Username=paymentqa;Password=***************;";
                //connectionString = "Host=**********;Port=5432;Database=payment-prod;Username=payment;Password=****************;";
                context.UseNpgsql(connectionString)
                    .UseTriggers(triggerOptions =>
                    {
                        triggerOptions
                            .AddTrigger<PaymentRequestAbortedStatusTrigger>()
                            .AddTrigger<TransactionAbortedStatusTrigger>()
                            .AddTrigger<CommandAbortedStatusTrigger>()
                            .AddTrigger<PayableAbortedStatusTrigger>()
                            .AddTrigger<PaymentRequestDetailsAbortedStatusTrigger>()
                            .AddTrigger<PaymentRequestFeeAbortedStatusTrigger>()
                            .AddTrigger<TransactionHistoryAbortedStatusTrigger>()
                            .AddTrigger<PaymentRequestForbidAmountChangesTrigger>()
                            .AddTrigger<PaymentRequestPayableForbidAmountChangesTrigger>()
                            .AddTrigger<PaymentRequestFeeForbidAmountChangesTrigger>()
                            .AddTrigger<PaymentTransactionForbidAmountChangesTrigger>();
                    });
            }
        });

        services.AddTransient(typeof(IGenericRepository<>), typeof(GenericRepository<>));

        services.AddTransient<IPaymentRequestRepository, PaymentRequestRepository>();
        services.AddTransient<IPaymentTransactionHistoryRepository, PaymentTransactionHistoryRepository>();
        services.AddTransient<IPaymentRequestCommandRepository, PaymentRequestCommandRepository>();
        services.AddTransient<IPaymentRequestPayableRepository, PaymentRequestPayableRepository>();
        services.AddTransient<IPaymentTransactionRepository, PaymentTransactionRepository>();
        services.AddTransient<ISequencesRepository, SequencesRepository>();
        services.AddTransient<IForbiddenCompanyRepository, ForbiddenCompanyRepository>();
        services.AddTransient<IPaymentRequestDetailsRepository, PaymentRequestDetailsRepository>();
        services.AddScoped<IEventLogRepository, EventLogRepository>();
        services.AddTransient<IPaymentConfigRepository, PaymentConfigRepository>();
    }
}